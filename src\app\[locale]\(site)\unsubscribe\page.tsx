import { Suspense } from "react";
import { notFound } from "next/navigation";
import { unsubscribeFromNewsletter } from "../actions/newsletter";
import { Button } from "@/components/ui/button";
import { Link } from "@/i18n/navigation";
import { getTranslations } from "next-intl/server";
import { Locale } from "@/i18n/routing";

async function UnsubscribeContent({
  token,
  locale,
}: {
  token: string;
  locale: Locale;
}) {
  const t = await getTranslations({ locale, namespace: "Newsletter" });

  if (!token) {
    return (
      <div className="from-primary to-primary-foreground flex min-h-screen items-center justify-center bg-gradient-to-br">
        <div className="mx-4 w-full max-w-md">
          <div className="rounded-lg bg-white p-8 text-center shadow-xl">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <svg
                className="h-8 w-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              {t("unsubscribe.invalidToken")}
            </h1>
            <p className="mb-6 text-gray-600">
              {t("unsubscribe.invalidTokenDescription")}
            </p>
            <Link href="/">
              <Button className="w-full">{t("unsubscribe.backToHome")}</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const result = await unsubscribeFromNewsletter(token);

  return (
    <div className="from-primary to-primary-foreground flex min-h-screen items-center justify-center bg-gradient-to-br">
      <div className="mx-4 w-full max-w-md">
        <div className="rounded-lg bg-white p-8 text-center shadow-xl">
          {result.success ? (
            <>
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <svg
                  className="h-8 w-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h1 className="mb-4 text-2xl font-bold text-gray-900">
                {t("unsubscribe.success")}
              </h1>
              <p className="mb-6 text-gray-600">
                {t("unsubscribe.successDescription")}
              </p>
            </>
          ) : (
            <>
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                <svg
                  className="h-8 w-8 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h1 className="mb-4 text-2xl font-bold text-gray-900">
                {t("unsubscribe.error")}
              </h1>
              <p className="mb-6 text-gray-600">
                {result.error || t("unsubscribe.errorDescription")}
              </p>
            </>
          )}

          <Link href="/">
            <Button className="w-full">{t("unsubscribe.backToHome")}</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

interface UnsubscribePageProps {
  params: Promise<{ locale: Locale }>;
  searchParams: Promise<{ token?: string }>;
}

export default async function UnsubscribePage({
  params,
  searchParams,
}: UnsubscribePageProps) {
  const { locale } = await params;
  const { token } = await searchParams;

  if (!token) {
    notFound();
  }

  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="border-primary h-32 w-32 animate-spin rounded-full border-b-2"></div>
        </div>
      }
    >
      <UnsubscribeContent token={token} locale={locale} />
    </Suspense>
  );
}
